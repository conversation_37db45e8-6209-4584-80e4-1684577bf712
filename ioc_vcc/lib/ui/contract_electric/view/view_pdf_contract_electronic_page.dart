import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:ioc_vcc/constants/assets.dart';
import 'package:ioc_vcc/constants/colors.dart';
import 'package:ioc_vcc/constants/style/font_size.dart';
import 'package:ioc_vcc/repository/api/urls.dart';
import 'package:ioc_vcc/ui/contract_electric/contract_electronic.dart';
import 'package:ioc_vcc/ui/impact_history/impact_history.dart';
import 'package:ioc_vcc/utils/style.dart';
import 'package:ioc_vcc/utils/widget/button/progress_button.dart';
import 'package:ioc_vcc/utils/widget/form_field.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:trackcarvcc/constants/enum.dart';
import 'package:trackcarvcc/controllers/mobile_call_controller.dart';
import 'package:trackcarvcc/models/mobile_call_status/mobile_call_request.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/utils/mobile_call/mobile_call.dart';

class ViewPdfContractElectronicPage extends GetView<ViewPdfContractElectronicController> {
  ViewPdfContractElectronicPage({this.contractArgumentsDto});

  final ContractArgumentsDto contractArgumentsDto;
  final TextEditingController rejectController = TextEditingController();
  final MobileCallController mobileCallController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF1976D2),
                Color(0xFF1565C0),
              ],
            ),
          ),
        ),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.white,
            size: 20,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        centerTitle: true,
        title: Text(
          'Xem file ký',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          ImpactHistoryWidget(
            orderId: contractArgumentsDto?.id,
            functionCode: contractArgumentsDto.description == "appendix" ? "APPENDIX_B2C" : "CONTRACT_B2C",
            iconButton: true,
            child: IconButton(
              icon: Icon(
                Icons.history,
                color: Colors.white,
                size: 24,
              ),
              onPressed: null, // Will be handled by ImpactHistoryWidget
              tooltip: 'Lịch sử tác động',
            ),
          ),
          SizedBox(width: 8),
        ],
      ),
      body: GetBuilder<ViewPdfContractElectronicController>(
        initState: (_) {
          /// get file path
          controller.viewFileSignContract(contractArgumentsDto.id, contractArgumentsDto.description);
        },
        builder: (_) {
          return Obx(() {
            return Column(
              children: [
                // PDF Viewer Container with modern design
                Expanded(
                  child: Container(
                    margin: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Stack(
                        children: [
                          Container(
                            width: double.infinity,
                            height: double.infinity,
                            color: Colors.grey[100],
                            child: controller.fileContractSignDto.value.filePath != null
                                ? SfPdfViewer.network(
                                    BASE_URL_STATIC + controller.fileContractSignDto.value.filePath,
                                    enableDocumentLinkAnnotation: false,
                                  )
                                : Center(
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        CircularProgressIndicator(
                                          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1976D2)),
                                        ),
                                        SizedBox(height: 16),
                                        Text(
                                          'Đang tải tài liệu...',
                                          style: TextStyle(
                                            color: Colors.grey[600],
                                            fontSize: 16,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                          ),
                          // Modern floating call button
                          Positioned(
                            top: 16,
                            right: 16,
                            child: Material(
                              elevation: 4,
                              borderRadius: BorderRadius.circular(28),
                              child: InkWell(
                                borderRadius: BorderRadius.circular(28),
                                onTap: () async {
                                  final resultCall = await MobileCallFunc.startCall(
                                    displayName: contractArgumentsDto?.partnerName,
                                    calleeNumber: contractArgumentsDto?.phonePartner,
                                    yctxCode: contractArgumentsDto?.code,
                                    caringService: "",
                                    employee: ""/*DataCenter.shared().getUserInfo().fullName*/,
                                    employeePhone: DataCenter.shared().getUserInfo().phoneNumber,
                                    callFrom: MobileCallFrom.IOC,
                                  );
                                  if(resultCall != null){
                                    MobileCallRequest request = MobileCallRequest(
                                      callType: 1,
                                      status: resultCall.status,
                                      callerNumber: resultCall.callerNumber,
                                      callID: resultCall.callID,
                                      duration: resultCall.duration,
                                      endTime: Platform.isIOS
                                          ? DateTime.fromMillisecondsSinceEpoch(
                                          (resultCall.endTime * 1000).round())
                                          : DateTime.fromMillisecondsSinceEpoch(
                                          (resultCall.endTime)),
                                      errorType: resultCall.errorType,
                                      ringingTime: Platform.isIOS
                                          ? DateTime.fromMillisecondsSinceEpoch(
                                          (resultCall.ringingTime * 1000).round())
                                          : DateTime.fromMillisecondsSinceEpoch(
                                          (resultCall.ringingTime)),
                                      startTime: Platform.isIOS
                                          ? DateTime.fromMillisecondsSinceEpoch(
                                          (resultCall.startTime * 1000).round())
                                          : DateTime.fromMillisecondsSinceEpoch(
                                          (resultCall.startTime)),
                                      // state: contractArgumentsDto?.contractElectronic?.state,
                                      tangentCustomerId: contractArgumentsDto?.id,
                                      type: contractArgumentsDto.description == "appendix" ? "APPENDIX_B2C" : "CONTRACT_B2C"
                                    );
                                    mobileCallController.postResultMobileCallIOC(request);
                                  }
                                },
                                child: Container(
                                  height: 56,
                                  width: 56,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [Color(0xFF4CAF50), Color(0xFF45A049)],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(28),
                                  ),
                                  child: Icon(
                                    Icons.phone,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),),
                // Modern action buttons container
                if (contractArgumentsDto.showButtonSign)
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: Offset(0, -2),
                          ),
                        ],
                      ),
                      padding: EdgeInsets.all(16),
                      child: SafeArea(
                        child: Row(
                          children: [
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () async {
                                  controller.createOTP(contractArgumentsDto.id, contractArgumentsDto.description);
                                },
                                icon: Icon(Icons.edit, size: 18),
                                label: Text(
                                  'Ký khách hàng',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                style: ElevatedButton.styleFrom(
                                  primary: Color(0xFF4CAF50),
                                  onPrimary: Colors.white,
                                  elevation: 2,
                                  padding: EdgeInsets.symmetric(vertical: 14),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () async {
                                  bottomSheetReject(context);
                                },
                                icon: Icon(Icons.close, size: 18),
                                label: Text(
                                  'Từ chối',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                style: ElevatedButton.styleFrom(
                                  primary: Color(0xFFF44336),
                                  onPrimary: Colors.white,
                                  elevation: 2,
                                  padding: EdgeInsets.symmetric(vertical: 14),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
              ],
            );
          });
        },
      ),
    );
  }

  void bottomSheetReject(BuildContext context) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        isDismissible: true,
        enableDrag: false,
        backgroundColor: Colors.white,
        builder: (BuildContext buildContext) {
          return Container(
            height: MediaQuery.of(context).size.height * 0.55,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(12)),
            ),
            padding: EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Từ chối ký'.toUpperCase(),
                      style: TextStyle(fontSize: FontSize.s20, fontWeight: FontWeight.bold),
                    ),
                    IconButton(
                      padding: const EdgeInsets.only(left: FontSize.s16),
                      icon: const Icon(
                        Icons.clear,
                        size: 25,
                        color: Colors.black,
                      ),
                      onPressed: () {
                        Get.back();
                      },
                    ),
                  ],
                ),
                SizedBox(
                  height: 5,
                ),
                FormFieldWidget(
                  labelText: 'Lý do từ chối',
                  child: FormBuilderTextField(
                    name: '',
                    controller: rejectController,
                    cursorColor: IocColorPrimary,
                    decoration: Style.inputDecorationBorder(
                      hintText: 'Nhập lý do từ chối',
                    ),
                    keyboardType: TextInputType.text,
                  ),
                ),
                SizedBox(
                  height: 5,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        child: Center(
                            child: Text(
                          'Hủy',
                          style: TextStyle(
                            color: Color(0xff0984F9),
                            fontSize: FontSize.s14,
                          ),
                        )),
                        width: MediaQuery.of(context).size.width / 3,
                        decoration: BoxDecoration(
                          border: Border.all(color: Color(0xff0984F9)),
                          borderRadius: BorderRadius.all(Radius.circular(4)),
                        ),
                        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                      ),
                    ),
                    GestureDetector(
                      onTap: () async {
                        if (rejectController.text.isEmpty) {
                          Fluttertoast.showToast(msg: 'Vui lòng nhập lý do từ chối');
                          return;
                        } else {
                          controller.reject(
                            contractId: contractArgumentsDto.id,
                            description: contractArgumentsDto.description,
                            reject: rejectController.text,
                          );
                          Get.back();
                        }
                      },
                      child: Container(
                        child: Center(
                            child: Text(
                          'Xác nhận',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: FontSize.s14,
                          ),
                        )),
                        width: MediaQuery.of(context).size.width / 3,
                        decoration: BoxDecoration(
                          color: Color(0xff0984F9),
                          borderRadius: BorderRadius.all(Radius.circular(4)),
                        ),
                        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        });
  }
}
