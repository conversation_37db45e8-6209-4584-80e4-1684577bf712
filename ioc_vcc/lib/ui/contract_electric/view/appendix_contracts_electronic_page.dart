import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ioc_vcc/constants/assets.dart';
import 'package:ioc_vcc/constants/app_constants.dart';
import 'package:ioc_vcc/constants/style/font_size.dart';
import 'package:ioc_vcc/constants/style/style.dart';
import 'package:ioc_vcc/routes/ioc_routes.dart';
import 'package:ioc_vcc/ui/contract_electric/contract_electronic.dart';

class AppendixContractElectronicPage extends GetView<AppendixContractsElectronicController> {
  final ScrollController scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // backgroundColor: Colors.white,
        flexibleSpace: Image(
          image: AssetImage(Assets.iconStatusBar),
          fit: BoxFit.cover,
        ),
        brightness: Brightness.light,
        elevation: 1.0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        centerTitle: true,
        title: Text(
          'Danh sách phụ lục hợp đồng',
          style: TextStyle(color: Colors.white, fontSize: 16),
        ),
      ),
      body: GetBuilder<AppendixContractsElectronicController>(
        initState: (_) {
          /// get first
          controller.getContractAppendixForElectricSignature();

          scrollController.addListener(() {
            if (scrollController.position.maxScrollExtent > scrollController.position.pixels &&
                scrollController.position.maxScrollExtent - scrollController.position.pixels <= MediaQuery.of(context).size.width) {
              Timer(const Duration(seconds: 1), () {
                controller.loadMore();
              });
            }
          });
        },
        builder: (_) {
          return Obx(() {
            return Column(
              children: [
                Container(
                  margin: EdgeInsets.symmetric(horizontal: FontSize.s12, vertical: 6),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.black12,
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: TextField(
                    // controller: searchController,
                    style: CommonTextStyle.normalTextStyle,
                    onChanged: (value) {
                      Timer(const Duration(milliseconds: 100), () {
                        controller.keySearch.value = value;
                        controller.getContractAppendixForElectricSignature();
                      });
                    },
                    decoration: const InputDecoration(
                      prefixIcon: Icon(Icons.search),
                      hintText: 'Tìm kiếm...',
                      border: InputBorder.none,
                    ),
                  ),
                ),
                Expanded(
                  child: controller.contractsElectronic.length > 0
                      ? ListView.separated(
                          controller: scrollController,
                          itemCount: controller.contractsElectronic.length,
                          physics: BouncingScrollPhysics(),
                          separatorBuilder: (context, index) {
                            return Divider();
                          },
                          itemBuilder: (context, index) {
                            var item = controller.contractsElectronic[index];
                            var contractArgumentsDto = ContractArgumentsDto(
                              id: item.contractPmxlId,
                              showButtonSign: item.showButtonSign,
                              description: AppConstant.APPENDIX_DESCRIPTION,
                              phonePartner: item?.phonePartner,
                              partnerName: item?.partnerName,
                              code: item?.appendixCode,
                            );
                            return AppendixContractElectronicItem(
                              item: item,
                              onClick: () async {
                                var result = await Get.toNamed(
                                  Routers.viewPdfContractElectronic,
                                  arguments: contractArgumentsDto,
                                );
                                if (result != null) {
                                  controller.getContractAppendixForElectricSignature();
                                }
                              },
                              index: index,
                            );
                          },
                        )
                      : Center(
                          child: Text(
                            "Không có dữ liệu",
                            style: CommonTextStyle.normalTextStyle,
                          ),
                        ),
                ),
                controller.isLoadMore.value ? Text('Loading...') : SizedBox(),
              ],
            );
          });
        },
      ),
    );
  }
}

class AppendixContractElectronicItem extends StatelessWidget {
  const AppendixContractElectronicItem({
    Key key,
    @required this.item,
    this.onClick,
    this.index,
  }) : super(key: key);

  final ContractElectronicDto item;
  final Function onClick;
  final int index;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onClick,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: FontSize.s12, vertical: 6),
        padding: EdgeInsets.all(FontSize.s12),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(
              Radius.circular(5),
            ),
            border: Border.all(color: Colors.black12)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$index. ${item.appendixCode}',
              style: CommonTextStyle.normalTextStyle.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: FontSize.s16,
              ),
            ),
            Divider(),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  'Mã hợp đồng',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    color: Colors.black54,
                    fontSize: FontSize.s13,
                  ),
                ),
                Text(
                  item.contractCode ?? '',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    fontSize: FontSize.s13,
                  ),
                ),
              ],
            ),
            Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Thời gian',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    color: Colors.black54,
                    fontSize: FontSize.s13,
                  ),
                ),
                Text(
                  'Từ ${item.startDateStr} đến ${item.endDateStr}',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    fontSize: FontSize.s13,
                  ),
                ),
              ],
            ),
            Divider(),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Đơn vị',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    color: Colors.black54,
                    fontSize: FontSize.s13,
                  ),
                ),
                Text(
                  item.signGroupName ?? '',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    fontSize: FontSize.s13,
                  ),
                ),
              ],
            ),
            Divider(),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Người tạo',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    color: Colors.black54,
                    fontSize: FontSize.s13,
                  ),
                ),
                Text(
                  item.createdUserName ?? '',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    fontSize: FontSize.s13,
                  ),
                ),
              ],
            ),
            Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Trạng thái ký',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    color: Colors.black54,
                  ),
                ),
                Text(
                  item.signStateName ?? '',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    color: Colors.black87,
                    fontWeight: FontWeight.w600,
                    fontSize: FontSize.s13,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
