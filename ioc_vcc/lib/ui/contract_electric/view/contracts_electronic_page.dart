import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ioc_vcc/constants/assets.dart';
import 'package:ioc_vcc/constants/style/font_size.dart';
import 'package:ioc_vcc/constants/style/style.dart';
import 'package:ioc_vcc/routes/ioc_routes.dart';
import 'package:ioc_vcc/ui/contract_electric/contract_electronic.dart';
import 'package:ioc_vcc/ui/intlabs/widget/convert.dart';

class ContractElectronicPage extends GetView<ContractsElectronicController> {
  final ScrollController scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // backgroundColor: Colors.white,
        flexibleSpace: Image(
          image: AssetImage(Assets.iconStatusBar),
          fit: BoxFit.cover,
        ),
        brightness: Brightness.light,
        elevation: 1.0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        centerTitle: true,
        title: Text(
          '<PERSON>h sách hợp đồng',
          style: TextStyle(color: Colors.white, fontSize: 16),
        ),
      ),
      body: GetBuilder<ContractsElectronicController>(
        initState: (_) {
          /// get first
          controller.getContractForElectricSignature();

          scrollController.addListener(() {
            // if (scrollController.position.maxScrollExtent == scrollController.offset) {
            //   Timer(const Duration(seconds: 1), () {
            //     controller.loadMore();
            //   });
            // }

            if (scrollController.position.maxScrollExtent > scrollController.position.pixels &&
                scrollController.position.maxScrollExtent - scrollController.position.pixels <= MediaQuery.of(context).size.width) {
              Timer(const Duration(seconds: 1), () {
                controller.loadMore();
              });
            }
          });
        },
        builder: (_) {
          return Obx(() {
            return Column(
              children: [
                Container(
                  margin: EdgeInsets.symmetric(horizontal: FontSize.s12, vertical: 6),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.black12,
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: TextField(
                    // controller: searchController,
                    style: CommonTextStyle.normalTextStyle,
                    onChanged: (value) {
                      Timer(const Duration(milliseconds: 100), () {
                        controller.keySearch.value = value;
                        controller.getContractForElectricSignature();
                      });
                    },
                    decoration: const InputDecoration(
                      prefixIcon: Icon(Icons.search),
                      hintText: 'Tìm kiếm...',
                      border: InputBorder.none,
                    ),
                  ),
                ),
                Expanded(
                  child: controller.contractsElectronic.length > 0
                      ? ListView.separated(
                          controller: scrollController,
                          itemCount: controller.contractsElectronic.length,
                          physics: BouncingScrollPhysics(),
                          separatorBuilder: (context, index) {
                            return Divider();
                          },
                          itemBuilder: (context, index) {
                            var item = controller.contractsElectronic[index];
                            var contractArgumentsDto = ContractArgumentsDto(
                              id: item.contractPmxlId,
                              showButtonSign: item.showButtonSign,
                              description: 'contract',
                              contractElectronic: item,
                              partnerName: item?.partnerName,
                              phonePartner: item?.phonePartner,
                              code: item?.contractCode,
                            );

                            return ContractElectronicItem(
                              item: item,
                              onClick: () async {
                                var result = await Get.toNamed(
                                  Routers.viewPdfContractElectronic,
                                  arguments: contractArgumentsDto,
                                );
                                if (result != null) {
                                  controller.getContractForElectricSignature();
                                }
                              },
                              index: index + 1,
                            );
                          },
                        )
                      : Center(
                          child: Text(
                            "Không có dữ liệu",
                            style: CommonTextStyle.normalTextStyle,
                          ),
                        ),
                ),
                controller.isLoadMore.value ? Text('Loading...') : SizedBox(),
              ],
            );
          });
        },
      ),
    );
  }
}

class ContractElectronicItem extends StatelessWidget {
  const ContractElectronicItem({
    Key key,
    @required this.item,
    this.onClick,
    this.index,
  }) : super(key: key);

  final ContractElectronicDto item;
  final Function onClick;
  final int index;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onClick,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: FontSize.s12, vertical: 6),
        padding: EdgeInsets.all(FontSize.s12),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(
              Radius.circular(5),
            ),
            border: Border.all(color: Colors.black12)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$index. ${item.contractCode}',
              style: CommonTextStyle.normalTextStyle.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: FontSize.s16,
              ),
            ),
            Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Thời gian',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    color: Colors.black54,
                    fontSize: FontSize.s13,
                  ),
                ),
                Text(
                  'Từ ${item.startDateStr} đến ${item.endDateStr}',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    fontSize: FontSize.s13,
                  ),
                ),
              ],
            ),
            Divider(),
            LayoutBuilder(
              builder: (context, constraints) {
                final isShort = (item.signGroupName?.length ?? 0) <= 30;

                return Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment:
                  isShort ? MainAxisAlignment.spaceBetween : MainAxisAlignment.start,
                  children: [
                    Text(
                      'Đơn vị',
                      style: CommonTextStyle.normalTextStyle.copyWith(
                        color: Colors.black54,
                        fontSize: FontSize.s13,
                      ),
                    ).paddingOnly(right: 8),
                    isShort
                        ? Text(
                      item.signGroupName ?? '',
                      style: CommonTextStyle.normalTextStyle.copyWith(
                        fontSize: FontSize.s13,
                      ),
                    )
                        : Expanded(
                      child: Text(
                        item.signGroupName ?? '',
                        style: CommonTextStyle.normalTextStyle.copyWith(
                          fontSize: FontSize.s13,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    )
                  ],
                );
              },
            ),
            Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tên KH',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    color: Colors.black54,
                    fontSize: FontSize.s13,
                  ),
                ),
                Text(
                  item.partnerName ?? '',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    fontSize: FontSize.s13,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                )
              ],
            ),
            Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Giá trị HĐ',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    color: Colors.black54,
                    fontSize: FontSize.s13,
                  ),
                ),
                Text(
                  '${Convert.money((item.price ?? 0) * 1.0)} VNĐ',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    fontSize: FontSize.s13,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                )
              ],
            ),
            Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Trạng thái ký',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    color: Colors.black54,
                  ),
                ),
                Text(
                  item.signStateName ?? '',
                  style: CommonTextStyle.normalTextStyle.copyWith(
                    color: Colors.black87,
                    fontWeight: FontWeight.w600,
                    fontSize: FontSize.s13,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
