import 'dart:typed_data';

import 'package:ioc_vcc/ui/contract_electric/uint8/unint8_converter.dart';
import 'package:json_annotation/json_annotation.dart';

part 'contract_electronic_dto.g.dart';

@JsonSerializable(explicitToJson: true)
class ContractElectronicDto {
  int contractPmxlId;
  String contractCode;
  String signGroupName;
  int price;
  String contractBranch;
  int state;
  String signDateStr;
  String startDateStr;
  String endDateStr;
  String provinceName;
  String stateStr;
  bool showButtonSign;
  String appendixCode;
  String createdUserName;
  String signStateName;
  String phonePartner;
  String partnerName;
  String planCode;
  int constructionId;

  ContractElectronicDto({
    this.contractPmxlId,
    this.contractCode,
    this.signGroupName,
    this.price,
    this.contractBranch,
    this.state,
    this.signDateStr,
    this.startDateStr,
    this.endDateStr,
    this.provinceName,
    this.stateStr,
    this.showButtonSign,
    this.signStateName,
    this.createdUserName,
    this.appendixCode,
    this.phonePartner,
    this.partnerName,
    this.planCode,
    this.constructionId,
  });

  factory ContractElectronicDto.fromJson(Map<String, dynamic> json) => _$ContractElectronicDtoFromJson(json);

  Map<String, dynamic> toJson() => _$ContractElectronicDtoToJson(this);
}

@JsonSerializable(
  explicitToJson: true,
  nullable: true,
)
class ContractElectronicRequest {
  @JsonKey(name: 'page')
  int page;

  @JsonKey(name: 'pageSize')
  int pageSize;

  @JsonKey(name: 'keySearch')
  String keySearch;

  @JsonKey(name: 'objectId')
  int objectId;

  @JsonKey(name: 'content')
  String content;

  @JsonKey(name: 'contractId')
  int contractId;

  @JsonKey(name: 'otpId')
  int otpId;

  @JsonKey(name: 'otpCode')
  String otpCode;

  @JsonKey(name: 'imagePath')
  @Uint8ListConverter()
  Uint8List imagePath;

  @JsonKey(name: 'description')
  String description;

  @JsonKey(name: 'type')
  String type;

  @JsonKey(name: 'rejectContent')
  String rejectContent;

  ContractElectronicRequest({
    this.page,
    this.pageSize,
    this.keySearch,
    this.objectId,
    this.content,
    this.contractId,
    this.otpId,
    this.otpCode,
    this.imagePath,
    this.description,
    this.type,
    this.rejectContent,
  });

  factory ContractElectronicRequest.fromJson(Map<String, dynamic> json) => _$ContractElectronicRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ContractElectronicRequestToJson(this);
}
