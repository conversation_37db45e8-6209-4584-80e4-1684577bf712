import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:google_map_location_picker/google_map_location_picker.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:ioc_vcc/constants/assets.dart';
import 'package:ioc_vcc/constants/colors.dart';
import 'package:ioc_vcc/constants/style/font_size.dart';
import 'package:ioc_vcc/routes/ioc_routes.dart';
import 'package:ioc_vcc/ui/intlabs/list_constructions/controller/list_constructions_controller.dart';
import 'package:ioc_vcc/ui/intlabs/list_constructions/model/reportDto.dart';
import 'package:ioc_vcc/ui/intlabs/list_constructions/ui/morning_report.dart';
import 'package:ioc_vcc/utils/debounce.dart';
import 'package:ioc_vcc/utils/widget/custom_text_form_field.dart';
import 'package:ioc_vcc/widget/button.dart';
import 'package:ioc_vcc/extensions/extensions.dart';
import 'package:trackcarvcc/repository/preferences/data_center.dart';
import 'package:trackcarvcc/widget/button/primary_button.dart';

class ListConstructions extends GetView<ListConstructionsController> {
  final TextEditingController searchController = TextEditingController();
  final Debounce _debounce = Debounce(Duration(milliseconds: 500));
  final user = DataCenter.shared().getUserInfo();

  onSelectedFilter(String choise) {
    print(choise);
    if (choise == 'Chưa thực hiện') {
      // controller.getListConstructions(1);
      controller.type = 1;
      controller.searchConstruction(searchController.text ?? '');
    }
    if (choise == 'Đang thực hiện') {
      // controller.getListConstructions(2);
      controller.type = 2;
      controller.searchConstruction(searchController.text ?? '');
    }
    if (choise == 'Đã hoàn thành') {
      // controller.getListConstructions(3);
      controller.type = 3;
      controller.searchConstruction(searchController.text ?? '');
    }
  }

  onSelectedOption(String choise, int index, BuildContext context) async {
    print(choise);
    if (choise == 'Gán nhân sự') {
      // controller.getListConstructions(1);
      print(choise);
      print(index);
    }
    if (choise == 'Gán Camera') {
      // controller.getListConstructions(2);
      Get.toNamed(Routers.list_camera_page,
          arguments: controller.listConstructions[index]);
    }
    if (choise == 'Gán Vị trí') {
      // controller.getListConstructions(2);
      LocationResult result = await showLocationPicker(
          context, "AIzaSyDc7PnOq3Hxzq6dxeUVaY8WGLHIePl0swY",
          initialCenter: LatLng(31.1975844, 29.9598339),
          automaticallyAnimateToCurrentLocation: true,
          mapStylePath: 'assets/mapStyle.json',
          myLocationButtonEnabled: true,
          requiredGPS: true,
          layersButtonEnabled: true,
          countries: ['AE', 'NG'],
          resultCardAlignment: Alignment.bottomCenter,
          desiredAccuracy: LocationAccuracy.best,
          language: 'vi');
      print("result = $result");

      if (result != null) {
        showDialogConfirm(context,
            titleDialog: 'Vị trí công trình',
            contentDialog:
                'Xác nhận tọa độ vị trí công trình (${result.latLng.latitude},${result.latLng.longitude}) ?',
            onYesCallback: () {
          Get.back();
          controller.updateLocation(
              controller.listConstructions[index].constructionId,
              result.latLng.latitude,
              result.latLng.longitude);
        });
      }
    }
    if (choise == 'Thêm nhật ký công trình') {
      // controller.getListConstructions(3);
      Get.toNamed(Routers.create_history_construction_page,
          arguments: controller.listConstructions[index]);
    }
    if (choise == 'Thêm cảnh báo') {
      controller.processProjectSelected = controller.listConstructions[index];
      Get.toNamed(Routers.add_alert,
          arguments: controller.listConstructions[index]);
      // controller.getListConstructions(3);
    }
    if (choise == 'Báo cáo ngày') {
      controller.processProjectSelected = controller.listConstructions[index];
      if (await controller.checkReportMorning()) {
        controller.listConstructions[index].routineTaskDTOList = controller.listRoutineTaskDTO;
        Get.toNamed(
          Routers.morning_report,
          arguments: MorningReportArgument(
            processProject: controller.listConstructions[index],
          ),
        );
      } else {
        final result = await controller.getListReports();

        if (result == true) {
          String today = DateFormat("dd/MM/yyyy").format(DateTime.now());
          ReportDTO report;
          for (int i = 0; i < controller.listReports.length; i++) {
            if (controller.listReports[i].reportDateStr == today) {
              report = controller.listReports[i];
              break;
            }
          }

          if (report != null) {
            Get.toNamed(
              Routers.morning_report,
              arguments: MorningReportArgument(
                processProject: controller.listConstructions[index],
                reportInfo: report,
              ),
            );
          } else {
            EasyLoading.showInfo('Bạn đã tạo báo cáo sáng');
          }
        }
      }
    }
    if (choise == 'Báo cáo kết quả ngày') {
      controller.processProjectSelected = controller.listConstructions[index];
      if (await controller.getDetailDayReports())
        Get.toNamed(Routers.day_report,
            arguments: controller.listConstructions[index]);
    }
    if (choise == 'Danh sách báo cáo ngày') {
      controller.processProjectSelected = controller.listConstructions[index];
      Get.toNamed(Routers.list_day_report,
          arguments: controller.listConstructions[index]);
    }
  }

  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return GetBuilder<ListConstructionsController>(
      initState: (_) {
        controller.getListConstructions(null);
        _setupScrollListener();
      },
      dispose: (_) {
        searchController.dispose();
        _debounce.dispose();
      },
      builder: (_) {
        return Scaffold(
          backgroundColor: IocBackground,
          appBar: AppBar(
            flexibleSpace: Image(
              image: AssetImage(Assets.iconStatusBar),
              fit: BoxFit.cover,
            ),
            elevation: 0.1,
            brightness: Brightness.light,
            centerTitle: true,
            title: Text('Danh sách công trình'),
            backgroundColor: IocColorPrimary,
            actions: [
              PopupMenuButton(
                  icon: Icon(Icons.filter_alt_rounded),
                  onSelected: onSelectedFilter,
                  itemBuilder: (BuildContext context) {
                    return controller.listTypeConstruction.map((String i) {
                      return PopupMenuItem(value: i, child: Text(i));
                    }).toList();
                  })
            ],
          ),
          body: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: CustomTextFormField(
                  controller: searchController,
                  hintText: "Tìm kiếm",
                  prefixIcon: Icon(
                    Icons.search,
                    color: IocColorPrimary,
                  ),
                  onChanged: (value) {
                    _debounce(() {
                      controller.searchConstruction(value);
                    });
                  },
                ),
              ),
              Expanded(
                child: Obx(() {
                  if (controller.isLoading.value && controller.listConstructions.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('Đang tải dữ liệu...'),
                        ],
                      ),
                    );
                  }

                  if (controller.listConstructions.isEmpty && !controller.isLoading.value) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.construction, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('Không có dữ liệu', style: TextStyle(color: Colors.grey)),
                          SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => controller.refreshData(),
                            child: Text('Thử lại'),
                          ),
                        ],
                      ),
                    );
                  }

                  // Show list with data
                  return RefreshIndicator(
                    onRefresh: () => controller.refreshData(),
                    child: ListView.builder(
                      controller: _scrollController,
                      padding: EdgeInsets.all(5),
                      itemCount: controller.listConstructions.length + (controller.hasMoreData.value ? 1 : 0),
                      itemBuilder: (BuildContext context, index) {
                        // Load more indicator
                        if (index == controller.listConstructions.length) {
                          return Obx(() {
                            if (controller.isLoadingMore.value) {
                              return Container(
                                padding: EdgeInsets.all(16),
                                alignment: Alignment.center,
                                child: CircularProgressIndicator(),
                              );
                            } else if (!controller.hasMoreData.value) {
                              return Container(
                                padding: EdgeInsets.all(16),
                                alignment: Alignment.center,
                                child: Text(
                                  'Đã tải hết dữ liệu',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              );
                            }
                            return SizedBox.shrink();
                          });
                        }
                          return Card(
                            child: InkWell(
                              onTap: () {
                                Get.toNamed(Routers.detail_project, arguments: [
                                  controller
                                      .listConstructions[index].constructionId,
                                  controller.listConstructions[index].planName
                                ]);
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(height: 5),
                                      Text(
                                        controller.listConstructions[index]
                                            .constructionCode
                                            .toString(),
                                        style: TextStyle(
                                            color: Colors.red,
                                            fontSize: FontSize.s15,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      if ((controller.listConstructions[index].duration ?? '').isNotEmpty)...[
                                        _itemDetail(
                                          'Thời gian thi công',
                                          controller.listConstructions[index].duration,
                                          true,
                                          icon: Icon(
                                            Icons.schedule,
                                            size: 18.0,
                                          ),
                                        ),
                                      ],
                                      if ((controller.listConstructions[index].stationCode ?? '').isNotEmpty) ...[
                                        _itemDetail(
                                          'Mã trạm',
                                          controller.listConstructions[index].stationCode,
                                          false,
                                        ),
                                      ],
                                      Row(
                                        children: <Widget>[
                                          Expanded(
                                            child: _itemDetail(
                                                'Hợp đồng',
                                                controller
                                                    .listConstructions[index]
                                                    .contractCode,
                                                false),
                                          ),
                                          Visibility(
                                            visible: !user.isWorkType,
                                            child: PopupMenuButton(
                                              icon: Icon(Icons.add_circle,
                                                  color: primaryColor),
                                              onSelected: (text) {
                                                onSelectedOption(
                                                  text,
                                                  index,
                                                  context,
                                                );
                                              },
                                              itemBuilder:
                                                  (BuildContext context) {
                                                return (
                                                (controller.listConstructions[index].templateTypeContract == 4 ||  controller.listConstructions[index].templateTypeContract == 5)
                                                ? controller.listOptionAddTK : controller.listOptionAdd).map((String i) {
                                                  return PopupMenuItem(
                                                      value: i, child: Text(i));
                                                }).toList();
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                      if ((controller.listConstructions[index]?.planCode ?? '').isNotEmpty) ...[
                                        _itemDetail(
                                          'Mã kế hoạch',
                                          controller.listConstructions[index].planCode,
                                          false,
                                        ),
                                      ],
                                      Visibility(
                                        visible: controller
                                                .listConstructions[index]
                                                .status ==
                                            5,
                                        child: GestureDetector(
                                            onTap: () {
                                              Get.toNamed(Routers.day_delivery,
                                                  arguments: [
                                                    controller
                                                        .listConstructions[
                                                            index]
                                                        .constructionCode,
                                                    controller
                                                        .listConstructions[
                                                            index]
                                                        .constructionId
                                                  ]);
                                            },
                                            child: Card(
                                              child: Container(
                                                  padding: EdgeInsets.all(10),
                                                  decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              5),
                                                      color: Colors.red),
                                                  child: Text(
                                                    'Cập nhật ngày bàn giao thực tế',
                                                    style: TextStyle(
                                                        color: Colors.white),
                                                  )),
                                            )),
                                      ),
                                      SizedBox(height: 16),
                                      if (user.isWorkType) ...[
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 24,
                                          ),
                                          child: PrimaryButton(
                                            height: 40,
                                            title: "Báo cáo ngày",
                                            color: IocColorPrimary,
                                            onTap: () async {
                                              controller
                                                      .processProjectSelected =
                                                  controller
                                                      .listConstructions[index];
                                              await controller
                                                  .checkReportMorning();
                                              controller
                                                      .listConstructions[index]
                                                      .routineTaskDTOList =
                                                  controller.listRoutineTaskDTO;
                                              Get.toNamed(
                                                  Routers.list_day_report,
                                                  arguments: controller
                                                          .listConstructions[
                                                      index]);
                                            },
                                          ),
                                        ),
                                        SizedBox(height: 12),
                                      ],
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                        )
                      );
                }),
              ),
            ],
          ),
        );
      },
    );
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
        // Load more when near bottom
        controller.loadMoreData();
      }
    });
  }

  _itemFilter(String title, onChoose, context) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        onChoose();
      },
      child: Column(
        children: [
          SizedBox(height: 10),
          Text(title),
          SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 50),
            child: Divider(
              thickness: 0.5,
              height: 0,
              color: Colors.grey,
            ),
          )
        ],
      ),
    );
  }

  _itemDetail(String title, String detail, bool isIcon, {Icon icon}) {
    return Column(
      children: [
        SizedBox(
          height: 10,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            !isIcon
                ? Text(
                    '$title: ',
                  )
                : icon,
            SizedBox(width: 8),
            Expanded(child: Text(detail))
          ],
        ),
      ],
    );
  }
}
