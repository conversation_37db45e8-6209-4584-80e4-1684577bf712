import '../constants/app_constants.dart';

class ContractUtils {
  /// Get function code based on contract description
  /// Returns CONTRACT_B2C for normal contracts and APPENDIX_B2C for appendix contracts
  static String getFunctionCode(String description) {
    if (description == AppConstant.APPENDIX_DESCRIPTION) {
      return AppConstant.APPENDIX_B2C_FUNCTION_CODE;
    }
    return AppConstant.CONTRACT_B2C_FUNCTION_CODE;
  }
  
  /// Check if the contract is an appendix contract
  static bool isAppendixContract(String description) {
    return description == AppConstant.APPENDIX_DESCRIPTION;
  }
  
  /// Get display name for contract type
  static String getContractTypeName(String description) {
    if (isAppendixContract(description)) {
      return "Phụ lục hợp đồng";
    }
    return "Hợp đồng";
  }
}
